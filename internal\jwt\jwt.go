package jwt

import (
	"context"
	"fmt"
	"shikeyinxiang/internal/cache"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"

	"shikeyinxiang/internal/config"
)

const (
	JWTBlacklistPrefix = "jwt:blacklist:jti:"
)

// Claims JWT声明结构
type Claims struct {
	UserID   int64  `json:"userId"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID int64, username, role string) (string, error) {
	cfg := config.AppConfig.JWT

	// 生成唯一的JWT ID
	jwtID := uuid.New().String()

	now := time.Now()
	expirationTime := now.Add(cfg.GetJWTExpiration())

	claims := Claims{
		UserID:   userID,
		Username: username,
		Role:     role,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jwtID,    // 添加JTI支持
			Subject:   username, // 使用username作为subject
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "shikeyinxiang",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.Secret))
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*Claims, error) {
	cfg := config.AppConfig.JWT

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(cfg.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// ValidateToken 验证JWT令牌（包含黑名单检查）
func ValidateToken(tokenString string) bool {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return false
	}

	// 检查JWT ID是否在黑名单中
	if claims.ID != "" {
		ctx := context.Background()
		exists, err := cache.Exists(ctx, JWTBlacklistPrefix+claims.ID)
		if err != nil {
			return false
		}
		if exists > 0 {
			return false // token在黑名单中
		}
	}

	// 检查是否过期
	return !claims.ExpiresAt.Time.Before(time.Now())
}

// BlacklistToken 将token加入黑名单
func BlacklistToken(tokenString string) error {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return err
	}

	// 计算剩余有效期
	ttl := time.Until(claims.ExpiresAt.Time)

	// 只有当token还未过期且有JTI时，才将其加入黑名单
	if ttl > 0 && claims.ID != "" {
		ctx := context.Background()
		return cache.Set(ctx, JWTBlacklistPrefix+claims.ID, "blacklisted", ttl)
	}

	return nil
}

// RefreshToken 刷新JWT令牌
func RefreshToken(tokenString string) (string, error) {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查令牌是否即将过期（剩余时间少于1小时）
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return "", fmt.Errorf("token does not need refresh")
	}

	return GenerateToken(claims.UserID, claims.Username, claims.Role)
}
