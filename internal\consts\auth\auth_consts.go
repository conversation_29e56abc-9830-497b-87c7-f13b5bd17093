package auth

// 错误码常量
const (
	// 用户登录时相关错误码
	CodeUserUserOrPasswordError = 401
	CodeUserDisabled            = 403
	CodeUsernameExists          = 409
	CodeEmailExists             = 409

	// 认证相关错误码
	CodeUnauthenticated = 401
)

// 错误消息常量
const (
	MsgUserUserOrPasswordError = "用户名或密码错误"
	MsgUserDisabled            = "用户已被封禁"
	MsgUsernameExists          = "用户名已存在"
	MsgEmailExists             = "邮箱已被注册"
	MsgUnauthenticated         = "认证失败"
)

// 用户状态常量
const (
	UserStatusActive   = 1 // 用户启用
	UserStatusDisabled = 0 // 用户禁用
)

// 用户角色常量
const (
	RoleUser  = "USER"  // 普通用户
	RoleAdmin = "ADMIN" // 管理员
)
