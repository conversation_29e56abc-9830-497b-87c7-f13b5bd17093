package v1

import "time"

// LoginReq 登录请求
type LoginReq struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"` // 用户名或邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"` // 密码
}

// LoginRes 登录响应
type LoginRes struct {
	User  *UserInfo `json:"user"`  // 用户信息
	Token string    `json:"token"` // JWT令牌
}

// RegisterReq 注册请求
type RegisterReq struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"`   // 用户名
	Email    string `json:"email" binding:"required" validate:"required,email,max=100"`     // 邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"`  // 密码
}

// RegisterRes 注册响应
type RegisterRes struct {
	User *UserInfo `json:"user"` // 用户信息
}

// UserInfo 用户信息（不包含敏感信息）
type UserInfo struct {
	ID         int64     `json:"id"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	Role       string    `json:"role"`
	Status     int8      `json:"status"`
	AvatarURL  string    `json:"avatarUrl,omitempty"`
	CreateTime time.Time `json:"createTime"`
}

// RefreshTokenRes 刷新令牌响应
type RefreshTokenRes struct {
	Token string `json:"token"`
}
