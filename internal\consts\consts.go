package consts

// 错误码常量
const (
	// 用户相关错误码
	CodeUserNotFound     = 1001
	CodePasswordError    = 1003
	CodeUserDisabled     = 403
	CodeUsernameExists   = 1002
	CodeEmailExists      = 1002
	
	// 认证相关错误码
	CodeTokenInvalid     = 401
	CodeUnauthorized     = 401
	
	// 参数相关错误码
	CodeInvalidParams    = 400
	
	// 系统相关错误码
	CodeInternalError    = 500
)

// 错误消息常量
const (
	MsgUserNotFound      = "用户不存在"
	MsgPasswordError     = "密码错误"
	MsgUserDisabled      = "用户已被禁用"
	MsgUsernameExists    = "用户名已存在"
	MsgEmailExists       = "邮箱已被注册"
	MsgTokenInvalid      = "令牌无效"
	MsgUnauthorized      = "未授权访问"
	MsgInvalidParams     = "参数错误"
	MsgInternalError     = "服务器内部错误"
)

// 用户状态常量
const (
	UserStatusActive   = 1 // 用户启用
	UserStatusDisabled = 0 // 用户禁用
)

// 用户角色常量
const (
	RoleUser  = "USER"  // 普通用户
	RoleAdmin = "ADMIN" // 管理员
)
