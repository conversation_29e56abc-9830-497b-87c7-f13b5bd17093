package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ApiResponse 统一API响应格式
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// PageResult 分页结果
type PageResult struct {
	Total   int64       `json:"total"`   // 总记录数
	Records interface{} `json:"records"` // 记录列表
	Current int         `json:"current"` // 当前页码
	Size    int         `json:"size"`    // 每页大小
}

// 错误消息映射
var messages = map[int]string{
	200:  "success",
	400:  "参数错误",
	401:  "未授权",
	403:  "禁止访问",
	404:  "资源不存在",
	500:  "系统内部错误",
	1001: "用户不存在",
	1002: "用户已存在",
	1003: "密码错误",
	1004: "令牌已过期",
	1005: "令牌无效",
}

// getMessage 获取错误消息
func getMessage(code int) string {
	if msg, ok := messages[code]; ok {
		return msg
	}
	return "未知错误"
}

// getHTTPStatus 根据业务错误码获取HTTP状态码
func getHTTPStatus(code int) int {
	switch {
	case code >= 400 && code < 500:
		return code // 直接使用HTTP状态码
	case code >= 1000 && code < 2000: // 用户相关错误
		switch code {
		case 1001: // 用户不存在（登录场景应返回401）
			return http.StatusUnauthorized
		case 1002: // 用户已存在
			return http.StatusConflict
		case 1003: // 密码错误
			return http.StatusUnauthorized
		case 1004, 1005: // 令牌相关错误
			return http.StatusUnauthorized
		default:
			return http.StatusBadRequest
		}
	default:
		return http.StatusOK // 成功或其他情况
	}
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应 - 自动映射HTTP状态码
func Error(c *gin.Context, code int) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, ApiResponse{
		Code:    code,
		Message: getMessage(code),
		Data:    nil,
	})
}

// ErrorWithMessage 错误响应（自定义消息）
func ErrorWithMessage(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, data interface{}, total int64, current, size int) {
	pageResult := PageResult{
		Total:   total,
		Records: data,
		Current: current,
		Size:    size,
	}
	Success(c, pageResult)
}


