package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// IAuth 认证服务接口
type IAuth interface {
	// Login 用户登录
	Login(ctx context.Context, req *v1.LoginReq) (*v1.LoginRes, error)
	
	// Register 用户注册
	Register(ctx context.Context, req *v1.RegisterReq) (*v1.RegisterRes, error)
	
	// Logout 用户登出
	Logout(ctx context.Context, token string) error
	
	// RefreshToken 刷新令牌
	RefreshToken(ctx context.Context, token string) (string, error)
}

// 全局服务实例
var (
	Auth IAuth
)
